package rebate

import (
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
)

var Rebate repo.RebateRepo = new(rebate)

type rebate struct {
	RebateRepository
}

type RebateRepository struct{}

func (r *RebateRepository) CreateUser(order *model.User) error {
	return global.GVA_DB.Create(order).Error
}
