type User {
  id: ID!
  email: String
  invitationCode: String
  createdAt: Time!
  updatedAt: Time!
  wallets: [UserWallet!]!
  referral: Referral
  referralSnapshot: ReferralSnapshot
  referrals: [Referral!]!
}

type UserWallet {
  id: ID!
  userId: ID!
  chain: String!
  name: String
  walletAddress: String!
  walletId: ID
  walletAccountId: ID
  walletType: WalletType
  createdAt: Time!
  updatedAt: Time!
  user: User!
}

type Referral {
  id: Int!
  userId: ID!
  referrerId: ID
  depth: Int!
  createdAt: Time!
  user: User!
  referrer: User
}

type ReferralSnapshot {
  userId: ID!
  directCount: Int!
  totalDownlineCount: Int!
  totalVolumeUsd: Float!
  totalRewardsDistributed: Float!
  user: User!
}

enum WalletType {
  EMBEDDED
  MANAGED
}

input CreateUserInput {
  email: String!
  invitationCode: String
  referrerCode: String
}

input CreateUserInvitationCodeInput {
  invitationCode: String!
  email: String
  chain: String
  name: String
  walletAddress: String
  walletId: ID
  walletAccountId: ID
  walletType: WalletType!
}

type CreateUserResponse {
  user: User!
  #token: String!
  success: Boolean!
  message: String!
}

input CreateUserWithReferralInput {
  invitationCode: String!
}
