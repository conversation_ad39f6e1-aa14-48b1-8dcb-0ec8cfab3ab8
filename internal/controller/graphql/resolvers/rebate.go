package resolvers

import (
	"github.com/go-playground/validator/v10"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/rebate"
)

type RebateResolver struct {
	s service.RebateI
	v *validator.Validate
}

func NewRebateResolver() *RebateResolver {
	return &RebateResolver{
		s: rebate.NewRebateService(),
		v: validator.New(),
	}
}
