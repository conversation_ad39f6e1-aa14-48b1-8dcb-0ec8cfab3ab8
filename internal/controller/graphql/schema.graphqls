scalar Time

directive @auth on FIELD_DEFINITION

type Query {
  # Get user by ID
  #user: User @auth

  # Get user wallets
  #userWallets: [UserWallet!]! @auth

  # Get referral information
  #referralInfo: Referral @auth

  # Get referral snapshot
  referralSnapshot: ReferralSnapshot @auth
}

type Mutation {
  # Create user with referral
  createUserWithReferral(input: CreateUserWithReferralInput!): CreateUserResponse! @auth

  # Create user invitation code
  createUserInvitationCode(input: CreateUserInvitationCodeInput!): CreateUserResponse! @auth
}
