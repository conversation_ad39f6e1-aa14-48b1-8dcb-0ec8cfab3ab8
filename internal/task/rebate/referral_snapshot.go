package rebate

import (
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gorm.io/gorm"
)

type SnapshotTask struct {
	repo repo.InvitationRepo
}

func NewSnapshotTask(repo repo.InvitationRepo) *SnapshotTask {
	return &SnapshotTask{
		repo: repo,
	}
}

func (s *SnapshotTask) UpdateAllReferralSnapshots() {
	var userIDs []uuid.UUID
	global.GVA_DB.Model(&model.User{}).Pluck("id", &userIDs)

	for _, userID := range userIDs {
		var directCount int64
		var totalDownlineCount int64

		// 统计直接下级（depth=1）
		global.GVA_DB.Model(&model.Referral{}).
			Where("referrer_id = ? AND depth = 1", userID).
			Count(&directCount)

		// 统计所有下级
		global.GVA_DB.Model(&model.Referral{}).
			Where("referrer_id = ? AND depth <= 3", userID).
			Count(&totalDownlineCount)

		// upsert ReferralSnapshot
		var snapshot model.ReferralSnapshot
		err := global.GVA_DB.Where("user_id = ?", userID).First(&snapshot).Error
		if err == gorm.ErrRecordNotFound {
			snapshot = model.ReferralSnapshot{
				UserID:             userID,
				DirectCount:        int(directCount),
				TotalDownlineCount: int(totalDownlineCount),
			}
			global.GVA_DB.Create(&snapshot)
		} else if err == nil {
			global.GVA_DB.Model(&snapshot).Updates(model.ReferralSnapshot{
				DirectCount:        int(directCount),
				TotalDownlineCount: int(totalDownlineCount),
			})
		} else {
			fmt.Printf("Error updating snapshot for user %s: %v\n", userID, err)
		}
	}
	fmt.Println("ReferralSnapshot Statistics completed")
}
