package service

import (
	"context"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

type (
	InvitationI interface {
		GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error)
		UpdateUserInvitationCode(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string,
			walletID, walletAccountID *uuid.UUID, walletType, invitationCode, email string) (*model.User, error)
		CreateUserWithReferral(ctx context.Context, referrerID uuid.UUID, userID string) error
		GetUserByInvitationCode(ctx context.Context, invitationCode string) (*model.User, error)
		GetUserByID(ctx context.Context, id uuid.UUID) (*model.User, error)
	}

	LevelI interface {
	}

	RebateI interface {
	}
)
