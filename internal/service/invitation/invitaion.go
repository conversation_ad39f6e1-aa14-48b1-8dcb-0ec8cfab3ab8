package invitation

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/invitation"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
	"gorm.io/gorm"
)

type InvitationService struct {
	invitationRepo invitation.InvitationRepository
}

func NewInvitationService() *InvitationService {
	return &InvitationService{
		invitationRepo: invitation.InvitationRepository{},
	}
}

func (i *InvitationService) GetReferralSnapshot(ctx context.Context, userID uuid.UUID) (*model.ReferralSnapshot, error) {
	return i.invitationRepo.GetReferralSnapshot(ctx, userID)
}

func (i *InvitationService) UpdateUserInvitationCode(ctx context.Context, userID uuid.UUID, chain, name, walletAddress string,
	walletID, walletAccountID *uuid.UUID, walletType, invitationCode, email string) (*model.User, error) {
	// Validate invitation code format
	if len(invitationCode) < 5 || len(invitationCode) > 15 {
		return nil, fmt.Errorf("invitation code must be 5-15 characters long")
	}

	// Normalize and validate email
	email = utils.NormalizeEmail(email)
	if !utils.IsValidEmail(email) {
		return nil, fmt.Errorf("invalid email format: %s", email)
	}

	// Only validate and convert chain if wallet information is provided
	var chainType model.ChainType
	var shouldCreateWallet bool

	if chain != "" && walletAddress != "" {
		shouldCreateWallet = true
		// Convert chain string to ChainType
		switch chain {
		case "EVM":
			chainType = model.ChainEvm
		case "ARB":
			chainType = model.ChainArb
		case "SOLANA":
			chainType = model.ChainSolana
		case "TRON":
			chainType = model.ChainTron
		default:
			return nil, fmt.Errorf("invalid chain type: %s", chain)
		}
	}

	// Use transaction to ensure atomicity
	return i.invitationRepo.WithTransaction(ctx, func(ctx context.Context) (*model.User, error) {
		// Check if user already exists
		existingUser, err := i.invitationRepo.GetByID(ctx, userID)
		if err != nil && err != gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("failed to check if user exists: %w", err)
		}

		var user *model.User
		if existingUser != nil {
			// User exists, update it
			existingUser.InvitationCode = &invitationCode
			if email != "" {
				existingUser.Email = utils.StringToPointer(email)
			}
			if err := i.invitationRepo.Update(ctx, existingUser); err != nil {
				return nil, fmt.Errorf("failed to update user: %w", err)
			}
			user = existingUser
		} else {
			// User doesn't exist, create new one
			newUser := &model.User{
				ID:             userID,
				InvitationCode: &invitationCode,
				Email:          utils.StringToPointer(email), // Set email as pointer, nil if empty
			}
			if err := i.invitationRepo.Create(ctx, newUser); err != nil {
				return nil, fmt.Errorf("failed to create user: %w", err)
			}
			user = newUser
		}

		// Only create wallet if chain and wallet address are provided
		if shouldCreateWallet {
			newUserWallet := &model.UserWallet{
				ID:              uuid.New(),
				UserID:          userID,
				Chain:           chainType,
				WalletAddress:   walletAddress,
				WalletID:        walletID,
				WalletAccountID: walletAccountID,
			}
			if err := i.invitationRepo.CreateWallet(ctx, newUserWallet); err != nil {
				return nil, fmt.Errorf("failed to create user wallet: %w", err)
			}
		}

		return user, nil
	})
}

func (i *InvitationService) CreateUserWithReferral(ctx context.Context, referrerID uuid.UUID, userID string) error {
	idParse, err := uuid.Parse(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID: %w", err)
	}

	// Check if user exists, create if not
	_, err = i.invitationRepo.GetByID(ctx, idParse)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// User doesn't exist, create a basic user record
			user := &model.User{
				ID: idParse,
			}
			if err := i.invitationRepo.Create(ctx, user); err != nil {
				return fmt.Errorf("failed to create user: %w", err)
			}
		} else {
			return fmt.Errorf("failed to check if user exists: %w", err)
		}
	}

	referral, err := i.invitationRepo.GetDirectReferral(ctx, idParse, referrerID)

	// If err is nil, the query is successful and the record is found.
	if err == nil && referral != nil {
		return fmt.Errorf("the direct referral relationship between user %s and referrer %s already exists", userID, referrerID.String())
	}

	// If err is not gorm.ErrRecordNotFound, it means that there is another database error and an error should also be reported.
	if err != nil && err != gorm.ErrRecordNotFound {
		return fmt.Errorf("database error when checking referral relationship: %w", err)
	}

	if err := i.createReferralRelationshipByID(ctx, idParse, referrerID.String()); err != nil {
		return err
	}
	return nil
}

func (i *InvitationService) createReferralRelationshipByID(ctx context.Context, userID uuid.UUID, referrerID string) error {
	referrerUUID, err := uuid.Parse(referrerID)
	if err != nil {
		return fmt.Errorf("invalid referrer ID: %w", err)
	}
	if referrerUUID == userID {
		return fmt.Errorf("cannot refer yourself")
	}
	// Check if the new user has been referred by anyone
	hasReferrer, err := i.invitationRepo.HasDirectReferral(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to check existing referrals for user %s: %w", userID, err)
	}
	if hasReferrer {
		return fmt.Errorf("user %s has already been referred by someone else", userID)
	}

	// Checks whether circular referrals are formed (B cannot recommend A if A has already recommended B)
	isCircular, err := i.invitationRepo.IsInUpline(ctx, referrerUUID, userID)
	if err != nil {
		return fmt.Errorf("failed to check for circular referral: %w", err)
	}
	if isCircular {
		return fmt.Errorf("circular referral detected: %s is already in the upline of %s", userID, referrerUUID)
	}

	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Create(&model.Referral{
			ReferrerID: &referrerUUID,
			UserID:     userID,
			Depth:      1,
		}).Error; err != nil {
			return fmt.Errorf("failed to create direct referral: %w", err)
		}

		referrerRelations, err := i.invitationRepo.GetAllReferrals(ctx, tx, referrerUUID)
		if err != nil {
			return fmt.Errorf("failed to get all referrals: %w", err)
		}

		for _, rel := range referrerRelations {
			if rel.ReferrerID.String() == userID.String() {
				continue
			}
			isCircular, err := i.invitationRepo.IsInUpline(ctx, referrerUUID, userID)
			if err != nil {
				continue
			}
			if isCircular {
				continue
			}
			if err := tx.Create(&model.Referral{
				ReferrerID: rel.ReferrerID,
				UserID:     userID,
				Depth:      rel.Depth + 1,
			}).Error; err != nil {
				return fmt.Errorf("failed to create indirect referral (depth %d): %w", rel.Depth+1, err)
			}
		}

		return nil
	})
}


func (i *InvitationService) GetUserByInvitationCode(ctx context.Context, invitationCode string) (*model.User, error) {
	return i.invitationRepo.GetByInvitationCode(ctx, invitationCode)
}

func (i *InvitationService) GetUserByID(ctx context.Context, id uuid.UUID) (*model.User, error) {
	return i.invitationRepo.GetByID(ctx, id)
}

func (i *InvitationService) CreateUserWallet(ctx context.Context, userID uuid.UUID, chain model.ChainType, walletAddress string, walletID *uuid.UUID, walletAccountID *uuid.UUID) (*model.UserWallet, error) {
	wallet := &model.UserWallet{
		UserID:          userID,
		Chain:           chain,
		WalletAddress:   walletAddress,
		WalletID:        walletID,
		WalletAccountID: walletAccountID,
	}

	if err := i.invitationRepo.CreateUserWallet(ctx, wallet); err != nil {
		return nil, fmt.Errorf("failed to create user wallet: %w", err)
	}

	return wallet, nil
}