package initializer

import (
	"context"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	repoInvitation "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/invitation"
	task2 "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/task/rebate"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"

	"go.uber.org/zap"
)

func InitTask() {
	scheduler := task2.NewTaskScheduler()
	ctx, cancel := context.WithCancel(context.Background())
	scheduler.SetCancelFunc(cancel)
	go scheduler.RunWithSignal(ctx)

	rebateTask := rebate.NewSnapshotTask(&repoInvitation.InvitationRepository{})
	err := scheduler.Register(global.GVA_CONFIG.CronTasks[utils.TaskReferralSnapshot].ID, global.GVA_CONFIG.CronTasks[utils.TaskReferralSnapshot].Cron, rebateTask.UpdateAllReferralSnapshots)
	if err != nil {
		global.GVA_LOG.Error("task  = ", zap.Any("err", err))
	}
}
